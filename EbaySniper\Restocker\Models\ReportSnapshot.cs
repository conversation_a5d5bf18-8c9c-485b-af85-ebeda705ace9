﻿using System;
using System.Collections.Generic;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a complete snapshot of a report including all data and grid configuration
    /// at the time the report was generated
    /// </summary>
    public class ReportSnapshot
    {
        /// <summary>
        /// Unique identifier for this report snapshot
        /// </summary>
        public string ReportId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// When this report snapshot was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// The filter criteria used to generate this report
        /// </summary>
        public ReportFilter Filter { get; set; }

        /// <summary>
        /// Grid column configuration at the time of report generation
        /// </summary>
        public List<GridColumnSnapshot> ColumnConfiguration { get; set; } = new List<GridColumnSnapshot>();

        /// <summary>
        /// Complete transaction and keyword data captured at report generation time
        /// </summary>
        public List<TransactionWithKeywordSnapshot> Data { get; set; } = new List<TransactionWithKeywordSnapshot>();
    }

    /// <summary>
    /// Represents the configuration of a grid column at the time of report generation
    /// </summary>
    public class GridColumnSnapshot
    {
        /// <summary>
        /// The field name that this column is bound to
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// The display caption of the column
        /// </summary>
        public string Caption { get; set; } = string.Empty;

        /// <summary>
        /// Whether the column was visible
        /// </summary>
        public bool Visible { get; set; }

        /// <summary>
        /// The visible index (order) of the column
        /// </summary>
        public int VisibleIndex { get; set; }

        /// <summary>
        /// The width of the column in pixels
        /// </summary>
        public int Width { get; set; }
    }

    /// <summary>
    /// Represents a transaction combined with the keyword state at the time of report generation
    /// </summary>
    public class TransactionWithKeywordSnapshot
    {
        /// <summary>
        /// The purchase transaction data
        /// </summary>
        public PurchaseTransaction Transaction { get; set; }

        /// <summary>
        /// The complete keyword state at the time of report generation
        /// </summary>
        public KeywordSnapshot KeywordState { get; set; }
    }
}
