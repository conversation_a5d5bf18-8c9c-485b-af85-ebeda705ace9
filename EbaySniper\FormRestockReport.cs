﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.Other;

namespace uBuyFirst
{
    /// <summary>
    /// Form for generating and displaying restock transaction reports
    /// </summary>
    public partial class FormRestockReport : XtraForm
    {
        private readonly IPurchaseTrackerRepository _repository;
        private readonly IReportService _reportService;
        private readonly IReportExportService _exportService;
        private readonly IKeywordDataService _keywordDataService;
        private readonly IReportSnapshotStorage _snapshotStorage;
        private readonly IItemHistoryExporter _itemHistoryExporter;
        private List<PurchaseTransaction> _currentTransactions;
        private List<EnrichedTransactionData> _currentEnrichedData;
        private ReportSnapshot _currentReportSnapshot;

        public FormRestockReport() : this(
            new PurchaseTrackerRepository(),
            new KeywordDataService(new List<Keyword2Find>()),
            new FileReportSnapshotStorage(Path.Combine(Folders.Settings, "Reports")))
        {
        }

        public FormRestockReport(IPurchaseTrackerRepository repository, IKeywordDataService keywordDataService, IReportSnapshotStorage snapshotStorage)
        {
            InitializeComponent();

            // Initialize services with dependency injection
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _keywordDataService = keywordDataService ?? throw new ArgumentNullException(nameof(keywordDataService));
            _snapshotStorage = snapshotStorage ?? throw new ArgumentNullException(nameof(snapshotStorage));
            _reportService = new ReportService(_repository);
            _exportService = new ReportExportService();

            // Initialize new RestockReporting system
            var historyOptions = new ItemHistoryOptions
            {
                BasePath = Path.Combine(Folders.Settings, "Reports", "ItemHistory")
            };
            _itemHistoryExporter = new ItemHistoryExporter(historyOptions);

            // Set default values
            spinEditDays.Value = 30;
            _currentTransactions = new List<PurchaseTransaction>();
            _currentEnrichedData = new List<EnrichedTransactionData>();

            // Configure grid
            ConfigureEnhancedGrid();
        }

        private void ConfigureEnhancedGrid()
        {
            var gridView = gridControlTransactions.MainView as GridView;
            if (gridView != null)
            {
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ColumnAutoWidth = false;
                gridView.OptionsBehavior.Editable = false;
                gridView.OptionsSelection.MultiSelect = false;

                // Clear existing columns
                gridView.Columns.Clear();

                // Add enhanced columns with keyword data
                AddGridColumn(gridView, "Alias", "Alias", 120, true, 0);
                AddGridColumn(gridView, "Keywords", "Keywords", 150, true, 1);
                AddGridColumn(gridView, "ItemTitle", "Item Title", 200, true, 2);
                AddGridColumn(gridView, "Status", "Status", 80, true, 3);
                AddGridColumn(gridView, "PurchasePrice", "Price", 80, true, 4, "c2");
                AddGridColumn(gridView, "Quantity", "Qty", 50, true, 5);
                AddGridColumn(gridView, "TotalValue", "Total", 80, true, 6, "c2");
                AddGridColumn(gridView, "RequiredQuantity", "Required", 70, true, 7);
                AddGridColumn(gridView, "PurchasedQuantity", "Purchased", 70, true, 8);
                AddGridColumn(gridView, "RemainingQuantity", "Remaining", 70, true, 9);
                AddGridColumn(gridView, "PriceMin", "Min Price", 80, true, 10, "c2");
                AddGridColumn(gridView, "PriceMax", "Max Price", 80, true, 11, "c2");
                AddGridColumn(gridView, "Condition", "Condition", 100, true, 12);
                AddGridColumn(gridView, "PurchaseDate", "Date", 120, true, 13, "g");
                AddGridColumn(gridView, "ItemId", "Item ID", 100, false, 14);
                AddGridColumn(gridView, "KeywordId", "Keyword ID", 100, false, 15);
                AddGridColumn(gridView, "JobId", "Job ID", 100, false, 16);
                AddGridColumn(gridView, "TransactionId", "Transaction ID", 120, false, 17);
                AddGridColumn(gridView, "PaymentMethod", "Payment", 100, false, 18);
                AddGridColumn(gridView, "EbaySiteName", "eBay Site", 80, false, 19);
                AddGridColumn(gridView, "LocatedIn", "Located In", 100, false, 20);
                AddGridColumn(gridView, "Sellers", "Sellers", 150, false, 21);
                AddGridColumn(gridView, "Categories", "Categories", 100, false, 22);
                AddGridColumn(gridView, "ViewName", "View", 80, false, 23);
                AddGridColumn(gridView, "Notes", "Notes", 200, false, 24);
            }
        }

        private void AddGridColumn(GridView gridView, string fieldName, string caption, int width, bool visible, int visibleIndex, string formatString = "")
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.Visible = visible;
            column.VisibleIndex = visible ? visibleIndex : -1;

            if (!string.IsNullOrEmpty(formatString))
            {
                column.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
                column.DisplayFormat.FormatString = formatString;
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                btnGenerateReport.Enabled = false;
                btnExport.Enabled = false;

                // Show loading
                lblStatus.Text = "Generating report...";

                var days = (int)spinEditDays.Value;
                var startDate = DateTime.Now.AddDays(-days);
                var endDate = DateTime.Now;

                // Create filter
                var filter = new ReportFilter
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = true
                };

                // Get transactions
                _currentTransactions = (await _repository.GetTransactionsByDateRangeAsync(startDate, endDate)).ToList();

                // Create enriched data with keyword information
                _currentEnrichedData = await CreateEnrichedTransactionDataAsync(_currentTransactions);

                // Bind enriched data to grid
                gridControlTransactions.DataSource = _currentEnrichedData;

                // Update summary
                UpdateSummary();

                // Create and save complete report snapshot
                _currentReportSnapshot = await CreateCompleteReportSnapshotAsync(filter);
                await _snapshotStorage.SaveSnapshotAsync(_currentReportSnapshot);

                // Check if comprehensive item history data is available for export
                var reportDays = (int)spinEditDays.Value;
                var reportStartDate = DateTime.Now.AddDays(-reportDays);
                var reportEndDate = DateTime.Now;
                var itemHistoryCount = await _itemHistoryExporter.GetHistoryCountAsync(reportStartDate, reportEndDate);

                lblStatus.Text = $"Report generated successfully. Found {_currentTransactions.Count} transactions and {itemHistoryCount} processed items.";
                btnExport.Enabled = itemHistoryCount > 0; // Enable export if there's comprehensive data available
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error generating report: {ex.Message}";
                XtraMessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateReport.Enabled = true;
            }
        }

        private void UpdateSummary()
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                lblTotalTransactions.Text = "Total Transactions: 0";
                lblTotalAmount.Text = "Total Amount: $0.00";
                lblCompletedTransactions.Text = "Completed: 0";
                lblFailedTransactions.Text = "Failed: 0";
                return;
            }

            var totalTransactions = _currentTransactions.Count;
            var totalAmount = _currentTransactions.Where(t => t.Status == "Completed").Sum(t => t.PurchasePrice * t.Quantity);
            var completedCount = _currentTransactions.Count(t => t.Status == "Completed");
            var failedCount = _currentTransactions.Count(t => t.Status == "Failed");

            lblTotalTransactions.Text = $"Total Transactions: {totalTransactions}";
            lblTotalAmount.Text = $"Total Amount: ${totalAmount:F2}";
            lblCompletedTransactions.Text = $"Completed: {completedCount}";
            lblFailedTransactions.Text = $"Failed: {failedCount}";
        }

        private async void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";
                    saveDialog.DefaultExt = "csv";
                    saveDialog.FileName = $"RestockReport_Comprehensive_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        btnExport.Enabled = false;
                        lblStatus.Text = "Exporting comprehensive item processing report...";

                        // Use the new RestockReporting system for comprehensive data export
                        var days = (int)spinEditDays.Value;
                        var startDate = DateTime.Now.AddDays(-days);
                        var endDate = DateTime.Now;

                        // Export using the new comprehensive RestockReporting system
                        await _itemHistoryExporter.ExportToCsvAsync(startDate, endDate, saveDialog.FileName);

                        // Get count for status message
                        var itemCount = await _itemHistoryExporter.GetHistoryCountAsync(startDate, endDate);

                        lblStatus.Text = $"Comprehensive report exported successfully. {itemCount} items processed.";
                        XtraMessageBox.Show($"Comprehensive report exported successfully!\n\nFile: {saveDialog.FileName}\nItems: {itemCount} processed items\nColumns: 47 comprehensive data columns\n\nThis report includes ALL items processed by restock filters, not just completed transactions.",
                            "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error exporting report: {ex.Message}";
                XtraMessageBox.Show($"Error exporting comprehensive report: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnExport.Enabled = true;
            }
        }

        private void FormRestockReport_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "Ready. Select number of days and click Generate Report for comprehensive item processing data.";
        }

        private void FormRestockReport_FormClosed(object sender, FormClosedEventArgs e)
        {
            // Clean up resources if needed
            _repository?.Dispose();
        }

        private void spinEditDays_EditValueChanged(object sender, EventArgs e)
        {
            // Clear current results when days value changes
            if (_currentTransactions?.Any() == true)
            {
                lblStatus.Text = "Days value changed. Click Generate Report to refresh data.";
            }
        }

        /// <summary>
        /// Creates enriched transaction data by combining transactions with keyword information
        /// </summary>
        private async Task<List<EnrichedTransactionData>> CreateEnrichedTransactionDataAsync(List<PurchaseTransaction> transactions)
        {
            var enrichedData = new List<EnrichedTransactionData>();

            // Get unique keyword IDs
            var keywordIds = transactions.Select(t => t.KeywordId).Distinct().ToList();

            // Get keyword data for all unique IDs
            var keywords = _keywordDataService.GetKeywordsByIds(keywordIds);
            var keywordDict = keywords.ToDictionary(k => k.Id, k => k);

            // Create enriched data for each transaction
            foreach (var transaction in transactions)
            {
                keywordDict.TryGetValue(transaction.KeywordId, out var keyword);
                var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);
                enrichedData.Add(enriched);
            }

            return enrichedData;
        }

        /// <summary>
        /// Creates a complete report snapshot with all current data and grid configuration
        /// </summary>
        private async Task<ReportSnapshot> CreateCompleteReportSnapshotAsync(ReportFilter filter)
        {
            // Capture current grid column configuration
            var columnConfig = CaptureGridColumnState();

            // Get unique keyword IDs from transactions
            var keywordIds = _currentTransactions.Select(t => t.KeywordId).Distinct().ToList();

            // Capture current state of all keywords
            var keywords = _keywordDataService.GetKeywordsByIds(keywordIds);
            var keywordSnapshots = keywords.ToDictionary(k => k.Id, k => KeywordSnapshot.FromKeyword2Find(k));

            // Create combined transaction + keyword snapshot data
            var snapshotData = _currentTransactions.Select(transaction =>
                new TransactionWithKeywordSnapshot
                {
                    Transaction = transaction,
                    KeywordState = keywordSnapshots.ContainsKey(transaction.KeywordId) ? keywordSnapshots[transaction.KeywordId] : null
                }).ToList();

            // Create complete snapshot
            return new ReportSnapshot
            {
                GeneratedAt = DateTime.Now,
                Filter = filter,
                ColumnConfiguration = columnConfig,
                Data = snapshotData
            };
        }

        /// <summary>
        /// Captures the current grid column configuration
        /// </summary>
        private List<GridColumnSnapshot> CaptureGridColumnState()
        {
            var gridView = gridControlTransactions.MainView as GridView;
            if (gridView == null) return new List<GridColumnSnapshot>();

            return gridView.Columns.Cast<GridColumn>()
                .Select(col => new GridColumnSnapshot
                {
                    FieldName = col.FieldName,
                    Caption = col.Caption,
                    Visible = col.Visible,
                    VisibleIndex = col.VisibleIndex,
                    Width = col.Width
                }).ToList();
        }

        /// <summary>
        /// Legacy method - replaced by comprehensive RestockReporting system
        /// This method is kept for backward compatibility but now redirects to the new system
        /// </summary>
        private async Task ExportReportSnapshotToCsvAsync(ReportSnapshot snapshot, string filePath)
        {
            // Redirect to new comprehensive reporting system
            var days = (int)spinEditDays.Value;
            var startDate = DateTime.Now.AddDays(-days);
            var endDate = DateTime.Now;

            await _itemHistoryExporter.ExportToCsvAsync(startDate, endDate, filePath);
        }
    }
}
