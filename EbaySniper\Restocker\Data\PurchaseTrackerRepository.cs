using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Data
{
    /// <summary>
    /// SQLite implementation of the purchase tracker repository
    /// </summary>
    public class PurchaseTrackerRepository : IPurchaseTrackerRepository
    {
        private readonly string _connectionString;
        private bool _disposed = false;
        private bool _isInitialized = false;
        private readonly object _initializationLock = new object();

        public PurchaseTrackerRepository(string connectionString = null)
        {
            _connectionString = connectionString ?? GetDefaultConnectionString();
        }

        /// <summary>
        /// Checks if the Restock functionality is enabled in the configuration
        /// </summary>
        /// <returns>True if RestockEnabled is true, false otherwise</returns>
        public static bool IsRestockEnabled()
        {
            return ConnectionConfig.RestockerEnabled;
        }

        private static string GetDefaultConnectionString()
        {
            if (string.IsNullOrEmpty(Folders.Settings))
            {
                throw new InvalidOperationException("Folders.Settings is not initialized. Call Folders.SetupFolders() first.");
            }

            var restockFolder = Path.Combine(Folders.Settings, "Restock");
            var dbPath = Path.Combine(restockFolder, "restock.db");
            return $"Data Source={dbPath};Version=3;";
        }

        public async Task InitializeDatabaseAsync()
        {
            if (_isInitialized)
                return;

            // Extract database path from connection string to determine folder
            var dbPath = ExtractDatabasePathFromConnectionString(_connectionString);
            if (!string.IsNullOrEmpty(dbPath))
            {
                var dbFolder = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(dbFolder) && !Directory.Exists(dbFolder))
                {
                    Directory.CreateDirectory(dbFolder);
                }
            }
            else if (string.IsNullOrEmpty(Folders.Settings))
            {
                throw new InvalidOperationException("Folders.Settings is not initialized. Call Folders.SetupFolders() first.");
            }
            else
            {
                // Fallback to using Folders.Settings for default path
                var restockFolder = Path.Combine(Folders.Settings, "Restock");
                if (!Directory.Exists(restockFolder))
                {
                    Directory.CreateDirectory(restockFolder);
                }
            }

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            // Create tables
            await CreateTablesAsync(connection);

            // Initialize default configuration
            await InitializeDefaultConfigurationAsync(connection);

            _isInitialized = true;
        }

        private static string ExtractDatabasePathFromConnectionString(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return string.Empty;

            // Parse connection string to extract Data Source
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2 && keyValue[0].Trim().Equals("Data Source", StringComparison.OrdinalIgnoreCase))
                {
                    return keyValue[1].Trim();
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// Ensures the database is initialized, but only if RestockEnabled is true
        /// This method handles the lifecycle issue where RestockEnabled starts false but becomes true later
        /// </summary>
        private async Task EnsureInitializedAsync()
        {
            if (!IsRestockEnabled())
            {
                throw new InvalidOperationException("Cannot access Restock database when RestockEnabled is false. Enable Restock functionality in ConnectionConfig first.");
            }

            await InitializeDatabaseAsync();
        }

        /// <summary>
        /// Creates database tables using the current schema (KeywordId/JobId-based)
        /// </summary>
        private async Task CreateTablesAsync(SQLiteConnection connection)
        {
            var createTableCommands = new[]
            {
                @"CREATE TABLE IF NOT EXISTS PurchaseRequirements (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    KeywordId TEXT NOT NULL,
                    KeywordAlias TEXT NOT NULL,
                    JobId TEXT NOT NULL,
                    RequiredQuantity INTEGER NOT NULL,
                    MaxPrice DECIMAL,
                    CreatedAt DATETIME NOT NULL,
                    UpdatedAt DATETIME NOT NULL,
                    IsActive BOOLEAN NOT NULL DEFAULT 1
                );",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_requirements_keyword_id
                  ON PurchaseRequirements(KeywordId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_requirements_job_id
                  ON PurchaseRequirements(JobId);",

                @"CREATE TABLE IF NOT EXISTS PurchaseTransactions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    KeywordId TEXT NOT NULL,
                    JobId TEXT NOT NULL,
                    ItemId TEXT NOT NULL,
                    ItemTitle TEXT NOT NULL,
                    PurchasePrice DECIMAL NOT NULL,
                    Quantity INTEGER NOT NULL,
                    PurchaseDate DATETIME NOT NULL,
                    Status TEXT NOT NULL,
                    TransactionId TEXT,
                    PaymentMethod TEXT,
                    ShippingAddress TEXT,
                    Notes TEXT,
                    LastStepHtml TEXT
                );",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_transactions_keyword_id
                  ON PurchaseTransactions(KeywordId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_transactions_job_id
                  ON PurchaseTransactions(JobId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_transactions_item_id
                  ON PurchaseTransactions(ItemId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_transactions_status
                  ON PurchaseTransactions(Status);",

                @"CREATE TABLE IF NOT EXISTS PurchaseAttempts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    KeywordId TEXT NOT NULL,
                    JobId TEXT NOT NULL,
                    ItemId TEXT NOT NULL,
                    AttemptDate DATETIME NOT NULL,
                    Result TEXT NOT NULL,
                    ErrorMessage TEXT
                );",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_attempts_keyword_id
                  ON PurchaseAttempts(KeywordId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_attempts_job_id
                  ON PurchaseAttempts(JobId);",

                @"CREATE INDEX IF NOT EXISTS idx_purchase_attempts_item_id
                  ON PurchaseAttempts(ItemId);",

                @"CREATE TABLE IF NOT EXISTS SyncHistory (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SyncDate DATETIME NOT NULL,
                    FileName TEXT NOT NULL,
                    RequirementsAdded INTEGER NOT NULL,
                    RequirementsUpdated INTEGER NOT NULL,
                    RequirementsDeactivated INTEGER NOT NULL
                );",

                @"CREATE INDEX IF NOT EXISTS idx_sync_history_sync_date
                  ON SyncHistory(SyncDate);",

                @"CREATE TABLE IF NOT EXISTS Configuration (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Key TEXT NOT NULL UNIQUE,
                    Value TEXT NOT NULL,
                    UpdatedAt DATETIME NOT NULL
                );",

                @"CREATE INDEX IF NOT EXISTS idx_configuration_key
                  ON Configuration(Key);"
            };

            foreach (var command in createTableCommands)
            {
                using var cmd = new SQLiteCommand(command, connection);
                await cmd.ExecuteNonQueryAsync();
            }
        }

        /// <summary>
        /// Async version of InitializeDefaultConfiguration for backward compatibility with tests
        /// </summary>
        private async Task InitializeDefaultConfigurationAsync(SQLiteConnection connection)
        {
            var defaultConfigs = new[]
            {
                ("AutoPurchaseEnabled", "false"),
                ("DefaultMaxPrice", "0"),
                ("NotifyOnPurchase", "true")
            };

            foreach (var (key, value) in defaultConfigs)
            {
                var checkCmd = new SQLiteCommand("SELECT COUNT(*) FROM Configuration WHERE Key = @key", connection);
                checkCmd.Parameters.AddWithValue("@key", key);
                var exists = Convert.ToInt32(await checkCmd.ExecuteScalarAsync()) > 0;

                if (!exists)
                {
                    var insertCmd = new SQLiteCommand(
                        "INSERT INTO Configuration (Key, Value, UpdatedAt) VALUES (@key, @value, @updatedAt)",
                        connection);
                    insertCmd.Parameters.AddWithValue("@key", key);
                    insertCmd.Parameters.AddWithValue("@value", value);
                    insertCmd.Parameters.AddWithValue("@updatedAt", DateTime.UtcNow);
                    await insertCmd.ExecuteNonQueryAsync();
                }
            }
        }

        public async Task<int> GetPurchasedQuantityAsync(string keywordId, string jobId)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(@"
                SELECT COALESCE(SUM(Quantity), 0)
                FROM PurchaseTransactions
                WHERE KeywordId = @keywordId AND JobId = @jobId AND Status = 'Completed'",
                connection);
            cmd.Parameters.AddWithValue("@keywordId", keywordId);
            cmd.Parameters.AddWithValue("@jobId", jobId);

            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<IEnumerable<PurchaseTransaction>> GetTransactionsByKeywordAndJobAsync(string keywordId, string jobId)
        {
            var transactions = new List<PurchaseTransaction>();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "SELECT * FROM PurchaseTransactions WHERE KeywordId = @keywordId AND JobId = @jobId ORDER BY PurchaseDate DESC",
                connection);
            cmd.Parameters.AddWithValue("@keywordId", keywordId);
            cmd.Parameters.AddWithValue("@jobId", jobId);

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                transactions.Add(MapToPurchaseTransaction(reader));
            }

            return transactions;
        }

        public async Task<IEnumerable<PurchaseTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var transactions = new List<PurchaseTransaction>();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "SELECT * FROM PurchaseTransactions WHERE PurchaseDate >= @startDate AND PurchaseDate <= @endDate ORDER BY PurchaseDate DESC",
                connection);
            cmd.Parameters.AddWithValue("@startDate", startDate);
            cmd.Parameters.AddWithValue("@endDate", endDate);

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                transactions.Add(MapToPurchaseTransaction(reader));
            }

            return transactions;
        }

        public async Task<int> AddTransactionAsync(PurchaseTransaction transaction)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(@"
                INSERT INTO PurchaseTransactions
                (KeywordId, JobId, ItemId, ItemTitle, PurchasePrice, Quantity, PurchaseDate, Status, TransactionId, PaymentMethod, ShippingAddress, Notes, LastStepHtml)
                VALUES (@keywordId, @jobId, @itemId, @itemTitle, @purchasePrice, @quantity, @purchaseDate, @status, @transactionId, @paymentMethod, @shippingAddress, @notes, @lastStepHtml);
                SELECT last_insert_rowid();", connection);

            AddTransactionParameters(cmd, transaction);
            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<bool> UpdateTransactionStatusAsync(int transactionId, string status)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "UPDATE PurchaseTransactions SET Status = @status WHERE Id = @id",
                connection);
            cmd.Parameters.AddWithValue("@status", status);
            cmd.Parameters.AddWithValue("@id", transactionId);

            var rowsAffected = await cmd.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<PurchaseAttempt>> GetAttemptsByKeywordAndJobAsync(string keywordId, string jobId)
        {
            var attempts = new List<PurchaseAttempt>();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "SELECT * FROM PurchaseAttempts WHERE KeywordId = @keywordId AND JobId = @jobId ORDER BY AttemptDate DESC",
                connection);
            cmd.Parameters.AddWithValue("@keywordId", keywordId);
            cmd.Parameters.AddWithValue("@jobId", jobId);

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                attempts.Add(MapToPurchaseAttempt(reader));
            }

            return attempts;
        }

        public async Task<int> AddAttemptAsync(PurchaseAttempt attempt)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(@"
                INSERT INTO PurchaseAttempts
                (KeywordId, JobId, ItemId, AttemptDate, Result, ErrorMessage)
                VALUES (@keywordId, @jobId, @itemId, @attemptDate, @result, @errorMessage);
                SELECT last_insert_rowid();", connection);

            AddAttemptParameters(cmd, attempt);
            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<IEnumerable<SyncHistory>> GetSyncHistoryAsync(int limit = 50)
        {
            var history = new List<SyncHistory>();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "SELECT * FROM SyncHistory ORDER BY SyncDate DESC LIMIT @limit",
                connection);
            cmd.Parameters.AddWithValue("@limit", limit);

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                history.Add(MapToSyncHistory(reader));
            }

            return history;
        }

        public async Task<int> AddSyncHistoryAsync(SyncHistory syncHistory)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(@"
                INSERT INTO SyncHistory
                (SyncDate, FileName, RequirementsAdded, RequirementsUpdated, RequirementsDeactivated)
                VALUES (@syncDate, @fileName, @requirementsAdded, @requirementsUpdated, @requirementsDeactivated);
                SELECT last_insert_rowid();", connection);

            AddSyncHistoryParameters(cmd, syncHistory);
            var result = await cmd.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<string> GetConfigurationValueAsync(string key)
        {
            await EnsureInitializedAsync();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand("SELECT Value FROM Configuration WHERE Key = @key", connection);
            cmd.Parameters.AddWithValue("@key", key);

            var result = await cmd.ExecuteScalarAsync();
            return result?.ToString();
        }

        public async Task SetConfigurationValueAsync(string key, string value)
        {
            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(@"
                INSERT OR REPLACE INTO Configuration (Key, Value, UpdatedAt)
                VALUES (@key, @value, @updatedAt)", connection);
            cmd.Parameters.AddWithValue("@key", key);
            cmd.Parameters.AddWithValue("@value", value);
            cmd.Parameters.AddWithValue("@updatedAt", DateTime.UtcNow);

            await cmd.ExecuteNonQueryAsync();
        }

        public async Task<IEnumerable<PurchaseTransaction>> GetAllTransactionsAsync()
        {
            var transactions = new List<PurchaseTransaction>();

            using var connection = new SQLiteConnection(_connectionString);
            await connection.OpenAsync();

            var cmd = new SQLiteCommand(
                "SELECT * FROM PurchaseTransactions ORDER BY PurchaseDate DESC",
                connection);

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                transactions.Add(MapToPurchaseTransaction(reader));
            }

            return transactions;
        }

        private void AddTransactionParameters(SQLiteCommand cmd, PurchaseTransaction transaction)
        {
            cmd.Parameters.AddWithValue("@keywordId", transaction.KeywordId);
            cmd.Parameters.AddWithValue("@jobId", transaction.JobId);
            cmd.Parameters.AddWithValue("@itemId", transaction.ItemId);
            cmd.Parameters.AddWithValue("@itemTitle", transaction.ItemTitle);
            cmd.Parameters.AddWithValue("@purchasePrice", transaction.PurchasePrice);
            cmd.Parameters.AddWithValue("@quantity", transaction.Quantity);
            cmd.Parameters.AddWithValue("@purchaseDate", transaction.PurchaseDate);
            cmd.Parameters.AddWithValue("@status", transaction.Status);
            cmd.Parameters.AddWithValue("@transactionId", (object)transaction.TransactionId ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@paymentMethod", (object)transaction.PaymentMethod ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@shippingAddress", (object)transaction.ShippingAddress ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@notes", (object)transaction.Notes ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@lastStepHtml", (object)transaction.LastStepHtml ?? DBNull.Value);
        }

        private void AddAttemptParameters(SQLiteCommand cmd, PurchaseAttempt attempt)
        {
            cmd.Parameters.AddWithValue("@keywordId", attempt.KeywordId);
            cmd.Parameters.AddWithValue("@jobId", attempt.JobId);
            cmd.Parameters.AddWithValue("@itemId", attempt.ItemId);
            cmd.Parameters.AddWithValue("@attemptDate", attempt.AttemptDate);
            cmd.Parameters.AddWithValue("@result", attempt.Result);
            cmd.Parameters.AddWithValue("@errorMessage", (object)attempt.ErrorMessage ?? DBNull.Value);
        }

        private void AddSyncHistoryParameters(SQLiteCommand cmd, SyncHistory syncHistory)
        {
            cmd.Parameters.AddWithValue("@syncDate", syncHistory.SyncDate);
            cmd.Parameters.AddWithValue("@fileName", syncHistory.FileName);
            cmd.Parameters.AddWithValue("@requirementsAdded", syncHistory.RequirementsAdded);
            cmd.Parameters.AddWithValue("@requirementsUpdated", syncHistory.RequirementsUpdated);
            cmd.Parameters.AddWithValue("@requirementsDeactivated", syncHistory.RequirementsDeactivated);
        }

        private PurchaseTransaction MapToPurchaseTransaction(IDataReader reader)
        {
            return new PurchaseTransaction
            {
                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                KeywordId = reader.GetString(reader.GetOrdinal("KeywordId")),
                JobId = reader.GetString(reader.GetOrdinal("JobId")),
                ItemId = reader.GetString(reader.GetOrdinal("ItemId")),
                ItemTitle = reader.GetString(reader.GetOrdinal("ItemTitle")),
                PurchasePrice = reader.GetDecimal(reader.GetOrdinal("PurchasePrice")),
                Quantity = reader.GetInt32(reader.GetOrdinal("Quantity")),
                PurchaseDate = reader.GetDateTime(reader.GetOrdinal("PurchaseDate")),
                Status = reader.GetString(reader.GetOrdinal("Status")),
                TransactionId = reader.IsDBNull(reader.GetOrdinal("TransactionId")) ? null : reader.GetString(reader.GetOrdinal("TransactionId")),
                PaymentMethod = reader.IsDBNull(reader.GetOrdinal("PaymentMethod")) ? null : reader.GetString(reader.GetOrdinal("PaymentMethod")),
                ShippingAddress = reader.IsDBNull(reader.GetOrdinal("ShippingAddress")) ? null : reader.GetString(reader.GetOrdinal("ShippingAddress")),
                Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                LastStepHtml = reader.IsDBNull(reader.GetOrdinal("LastStepHtml")) ? null : reader.GetString(reader.GetOrdinal("LastStepHtml"))
            };
        }

        private PurchaseAttempt MapToPurchaseAttempt(IDataReader reader)
        {
            return new PurchaseAttempt
            {
                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                KeywordId = reader.GetString(reader.GetOrdinal("KeywordId")),
                JobId = reader.GetString(reader.GetOrdinal("JobId")),
                ItemId = reader.GetString(reader.GetOrdinal("ItemId")),
                AttemptDate = reader.GetDateTime(reader.GetOrdinal("AttemptDate")),
                Result = reader.GetString(reader.GetOrdinal("Result")),
                ErrorMessage = reader.IsDBNull(reader.GetOrdinal("ErrorMessage")) ? null : reader.GetString(reader.GetOrdinal("ErrorMessage"))
            };
        }

        private SyncHistory MapToSyncHistory(IDataReader reader)
        {
            return new SyncHistory
            {
                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                SyncDate = reader.GetDateTime(reader.GetOrdinal("SyncDate")),
                FileName = reader.GetString(reader.GetOrdinal("FileName")),
                RequirementsAdded = reader.GetInt32(reader.GetOrdinal("RequirementsAdded")),
                RequirementsUpdated = reader.GetInt32(reader.GetOrdinal("RequirementsUpdated")),
                RequirementsDeactivated = reader.GetInt32(reader.GetOrdinal("RequirementsDeactivated"))
            };
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Cleanup if needed
                _disposed = true;
            }
        }
    }
}
