using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class JsonSerializationTests
    {
        [TestMethod]
        public void ReportSnapshot_NewtonsoftJsonSerialization_WorksCorrectly()
        {
            // Arrange
            var originalSnapshot = CreateTestReportSnapshot();

            // Act
            var json = JsonConvert.SerializeObject(originalSnapshot, Formatting.Indented);
            var deserializedSnapshot = JsonConvert.DeserializeObject<ReportSnapshot>(json);

            // Assert
            Assert.IsNotNull(deserializedSnapshot);
            Assert.AreEqual(originalSnapshot.ReportId, deserializedSnapshot.ReportId);
            Assert.AreEqual(originalSnapshot.GeneratedAt, deserializedSnapshot.GeneratedAt);
            Assert.AreEqual(originalSnapshot.ColumnConfiguration.Count, deserializedSnapshot.ColumnConfiguration.Count);
            Assert.AreEqual(originalSnapshot.Data.Count, deserializedSnapshot.Data.Count);

            // Verify first column configuration
            var originalCol = originalSnapshot.ColumnConfiguration[0];
            var deserializedCol = deserializedSnapshot.ColumnConfiguration[0];
            Assert.AreEqual(originalCol.FieldName, deserializedCol.FieldName);
            Assert.AreEqual(originalCol.Caption, deserializedCol.Caption);
            Assert.AreEqual(originalCol.Visible, deserializedCol.Visible);
            Assert.AreEqual(originalCol.VisibleIndex, deserializedCol.VisibleIndex);
            Assert.AreEqual(originalCol.Width, deserializedCol.Width);

            // Verify transaction data
            var originalTransaction = originalSnapshot.Data[0].Transaction;
            var deserializedTransaction = deserializedSnapshot.Data[0].Transaction;
            Assert.AreEqual(originalTransaction.Id, deserializedTransaction.Id);
            Assert.AreEqual(originalTransaction.ItemTitle, deserializedTransaction.ItemTitle);
            Assert.AreEqual(originalTransaction.PurchasePrice, deserializedTransaction.PurchasePrice);

            // Verify keyword snapshot
            var originalKeyword = originalSnapshot.Data[0].KeywordState;
            var deserializedKeyword = deserializedSnapshot.Data[0].KeywordState;
            Assert.AreEqual(originalKeyword.KeywordId, deserializedKeyword.KeywordId);
            Assert.AreEqual(originalKeyword.Alias, deserializedKeyword.Alias);
            Assert.AreEqual(originalKeyword.Keywords, deserializedKeyword.Keywords);
        }

        [TestMethod]
        public void KeywordSnapshot_NewtonsoftJsonSerialization_WorksCorrectly()
        {
            // Arrange
            var originalKeyword = CreateTestKeyword2Find();
            var originalSnapshot = KeywordSnapshot.FromKeyword2Find(originalKeyword);

            // Act
            var json = JsonConvert.SerializeObject(originalSnapshot, Formatting.Indented);
            var deserializedSnapshot = JsonConvert.DeserializeObject<KeywordSnapshot>(json);

            // Assert
            Assert.IsNotNull(deserializedSnapshot);
            Assert.AreEqual(originalSnapshot.KeywordId, deserializedSnapshot.KeywordId);
            Assert.AreEqual(originalSnapshot.Alias, deserializedSnapshot.Alias);
            Assert.AreEqual(originalSnapshot.Keywords, deserializedSnapshot.Keywords);
            Assert.AreEqual(originalSnapshot.RequiredQuantity, deserializedSnapshot.RequiredQuantity);
            Assert.AreEqual(originalSnapshot.PurchasedQuantity, deserializedSnapshot.PurchasedQuantity);
            Assert.AreEqual(originalSnapshot.PriceMin, deserializedSnapshot.PriceMin);
            Assert.AreEqual(originalSnapshot.PriceMax, deserializedSnapshot.PriceMax);

            // Verify arrays are properly serialized
            Assert.AreEqual(originalSnapshot.Condition.Length, deserializedSnapshot.Condition.Length);
            Assert.AreEqual(originalSnapshot.Sellers.Length, deserializedSnapshot.Sellers.Length);
            Assert.AreEqual(originalSnapshot.ListingType.Length, deserializedSnapshot.ListingType.Length);
        }

        [TestMethod]
        public void EnrichedTransactionData_NewtonsoftJsonSerialization_WorksCorrectly()
        {
            // Arrange
            var transaction = CreateTestPurchaseTransaction();
            var keyword = CreateTestKeyword2Find();
            var originalEnriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Act
            var json = JsonConvert.SerializeObject(originalEnriched, Formatting.Indented);
            var deserializedEnriched = JsonConvert.DeserializeObject<EnrichedTransactionData>(json);

            // Assert
            Assert.IsNotNull(deserializedEnriched);

            // Verify transaction properties
            Assert.AreEqual(originalEnriched.Id, deserializedEnriched.Id);
            Assert.AreEqual(originalEnriched.ItemTitle, deserializedEnriched.ItemTitle);
            Assert.AreEqual(originalEnriched.PurchasePrice, deserializedEnriched.PurchasePrice);

            // Verify keyword properties
            Assert.AreEqual(originalEnriched.Alias, deserializedEnriched.Alias);
            Assert.AreEqual(originalEnriched.Keywords, deserializedEnriched.Keywords);
            Assert.AreEqual(originalEnriched.RequiredQuantity, deserializedEnriched.RequiredQuantity);

            // Verify calculated properties
            Assert.AreEqual(originalEnriched.RemainingQuantity, deserializedEnriched.RemainingQuantity);
            Assert.AreEqual(originalEnriched.CompletionPercentage, deserializedEnriched.CompletionPercentage);
            Assert.AreEqual(originalEnriched.TotalValue, deserializedEnriched.TotalValue);
        }

        [TestMethod]
        public void JsonSerialization_HandlesNullValues_Gracefully()
        {
            // Arrange
            var snapshot = new ReportSnapshot
            {
                ReportId = "test-null-handling",
                GeneratedAt = DateTime.Now,
                Filter = null, // Null filter
                ColumnConfiguration = new List<GridColumnSnapshot>(),
                Data = new List<TransactionWithKeywordSnapshot>
                {
                    new TransactionWithKeywordSnapshot
                    {
                        Transaction = CreateTestPurchaseTransaction(),
                        KeywordState = null // Null keyword state
                    }
                }
            };

            // Act
            var json = JsonConvert.SerializeObject(snapshot, Formatting.Indented);
            var deserializedSnapshot = JsonConvert.DeserializeObject<ReportSnapshot>(json);

            // Assert
            Assert.IsNotNull(deserializedSnapshot);
            Assert.AreEqual(snapshot.ReportId, deserializedSnapshot.ReportId);
            Assert.IsNull(deserializedSnapshot.Filter);
            Assert.IsNull(deserializedSnapshot.Data[0].KeywordState);
            Assert.IsNotNull(deserializedSnapshot.Data[0].Transaction);
        }

        private ReportSnapshot CreateTestReportSnapshot()
        {
            return new ReportSnapshot
            {
                ReportId = "test-report-123",
                GeneratedAt = new DateTime(2024, 12, 19, 14, 30, 0),
                Filter = new ReportFilter
                {
                    StartDate = new DateTime(2024, 11, 19),
                    EndDate = new DateTime(2024, 12, 19),
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = false
                },
                ColumnConfiguration = new List<GridColumnSnapshot>
                {
                    new GridColumnSnapshot
                    {
                        FieldName = "ItemTitle",
                        Caption = "Item Title",
                        Visible = true,
                        VisibleIndex = 0,
                        Width = 200
                    }
                },
                Data = new List<TransactionWithKeywordSnapshot>
                {
                    new TransactionWithKeywordSnapshot
                    {
                        Transaction = CreateTestPurchaseTransaction(),
                        KeywordState = KeywordSnapshot.FromKeyword2Find(CreateTestKeyword2Find())
                    }
                }
            };
        }

        private PurchaseTransaction CreateTestPurchaseTransaction()
        {
            return new PurchaseTransaction
            {
                Id = 1,
                KeywordId = "keyword-123",
                JobId = "job-456",
                ItemId = "item-789",
                ItemTitle = "Test Item",
                PurchasePrice = 25.99m,
                Quantity = 2,
                PurchaseDate = new DateTime(2024, 12, 19, 10, 30, 0),
                Status = "Completed",
                TransactionId = "txn-123",
                PaymentMethod = "PayPal",
                ShippingAddress = "123 Test St",
                Notes = "Test purchase"
            };
        }

        private Keyword2Find CreateTestKeyword2Find()
        {
            return new Keyword2Find
            {
                Id = "keyword-123",
                Alias = "Test Product",
                Kws = "test,product,keywords",
                JobId = "job-456",
                RequiredQuantity = 5,
                PurchasedQuantity = 2,
                PriceMin = 10.0,
                PriceMax = 50.0,
                Categories4Api = "12345",
                EbaySiteName = "eBay US",
                LocatedIn = "United States",
                AvailableTo = "Worldwide",
                Zip = "12345",
                SellerType = "Include",
                ViewName = "TestView",
                SearchInDescription = true,
                Condition = new[] { "New", "Used" },
                Sellers = new[] { "seller1", "seller2" },
                ListingType = new[] { uBuyFirst.ListingType.AuctionsStartedNow, uBuyFirst.ListingType.BuyItNow }
            };
        }
    }
}
