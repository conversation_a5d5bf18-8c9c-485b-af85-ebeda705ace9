using System;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Other;

namespace uBuyFirst
{
    /// <summary>
    /// Simple test to verify ItemHistory changes work correctly
    /// </summary>
    public class TestItemHistoryChanges
    {
        public static async Task RunTest()
        {
            Console.WriteLine("=== Testing ItemHistory Changes ===");
            
            // Initialize Folders (normally done at startup)
            Folders.SetupFolders();
            
            // Test 1: Verify default options use Folders.Settings
            Console.WriteLine("Test 1: Checking default options path...");
            var defaultOptions = ItemHistoryOptions.CreateDefault();
            var expectedPath = Path.Combine(Folders.Settings, "Reports", "ItemHistory");
            
            if (defaultOptions.BasePath == expectedPath)
            {
                Console.WriteLine($"✓ PASS: Default path correctly uses Folders.Settings: {defaultOptions.BasePath}");
            }
            else
            {
                Console.WriteLine($"✗ FAIL: Expected {expectedPath}, got {defaultOptions.BasePath}");
                return;
            }
            
            // Test 2: Verify file naming format
            Console.WriteLine("\nTest 2: Checking file naming format...");
            var testPath = Path.Combine(Path.GetTempPath(), "ItemHistoryTest", Guid.NewGuid().ToString());
            Directory.CreateDirectory(testPath);
            
            try
            {
                var options = new ItemHistoryOptions
                {
                    BasePath = testPath,
                    ErrorLogPath = Path.Combine(testPath, "errors"),
                    EnableLogging = true,
                    CreateDailyFolders = true
                };
                
                var logger = new FileItemHistoryLogger(options);
                
                // Create test context
                var context = new ItemProcessingContext
                {
                    Timestamp = DateTime.UtcNow,
                    Outcome = "purchased",
                    Reason = "Test purchase",
                    ItemData = new ItemHistoryData
                    {
                        ItemId = "123456789",
                        Title = "Test Item",
                        CurrentPrice = 25.99m
                    },
                    KeywordState = new KeywordSnapshot
                    {
                        KeywordId = "kw-test-123",
                        JobId = "JOB-001",
                        Alias = "Test Keyword"
                    }
                };
                
                // Test file name generation
                var fileName = logger.GenerateFileName(context);
                var expectedFileName = "123456789_JOB-001_kw-test-123.json";
                
                if (fileName == expectedFileName)
                {
                    Console.WriteLine($"✓ PASS: File name format correct: {fileName}");
                }
                else
                {
                    Console.WriteLine($"✗ FAIL: Expected {expectedFileName}, got {fileName}");
                    return;
                }
                
                // Test 3: Verify file overwrite behavior
                Console.WriteLine("\nTest 3: Testing file overwrite behavior...");
                
                // Log first context
                await logger.LogItemProcessingAsync(context);
                
                // Modify context and log again
                context.Reason = "Updated test purchase";
                await logger.LogItemProcessingAsync(context);
                
                // Check that only one file exists
                var todayFolder = Path.Combine(testPath, DateTime.UtcNow.ToString("yyyy-MM-dd"));
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                
                if (jsonFiles.Length == 1)
                {
                    Console.WriteLine($"✓ PASS: Only one file exists (overwrite behavior working)");
                    
                    // Verify content is updated
                    var content = await File.ReadAllTextAsync(jsonFiles[0]);
                    if (content.Contains("Updated test purchase"))
                    {
                        Console.WriteLine($"✓ PASS: File content was overwritten correctly");
                    }
                    else
                    {
                        Console.WriteLine($"✗ FAIL: File content was not updated");
                        return;
                    }
                }
                else
                {
                    Console.WriteLine($"✗ FAIL: Expected 1 file, found {jsonFiles.Length}");
                    return;
                }
                
                Console.WriteLine("\n=== All Tests Passed! ===");
                Console.WriteLine($"Files are now saved to: {Folders.Settings}\\Reports\\ItemHistory\\YYYY-MM-DD\\");
                Console.WriteLine($"File naming format: ItemId_JobId_KeywordId.json");
                Console.WriteLine($"Files are overwritten if they exist (no sequence numbers)");
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(testPath))
                {
                    Directory.Delete(testPath, true);
                }
            }
        }
    }
}
