using System;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents transaction data enriched with keyword properties for display in reports
    /// </summary>
    public class EnrichedTransactionData
    {
        // Transaction properties
        public int Id { get; set; }
        public string KeywordId { get; set; } = string.Empty;
        public string JobId { get; set; } = string.Empty;
        public string ItemId { get; set; } = string.Empty;
        public string ItemTitle { get; set; } = string.Empty;
        public decimal PurchasePrice { get; set; }
        public int Quantity { get; set; }
        public DateTime PurchaseDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string ShippingAddress { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;

        // Keyword properties for enhanced display
        public string Alias { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public bool SearchInDescription { get; set; }
        public double PriceMin { get; set; }
        public double PriceMax { get; set; }
        public string Categories { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string EbaySiteName { get; set; } = string.Empty;
        public string LocatedIn { get; set; } = string.Empty;
        public string AvailableTo { get; set; } = string.Empty;
        public string Zip { get; set; } = string.Empty;
        public string Sellers { get; set; } = string.Empty;
        public string SellerType { get; set; } = string.Empty;
        public TimeSpan Frequency { get; set; }
        public int Threads { get; set; }
        public string ViewName { get; set; } = string.Empty;
        public string ListingType { get; set; } = string.Empty;
        public int RequiredQuantity { get; set; }
        public int PurchasedQuantity { get; set; }

        // Calculated properties
        public int RemainingQuantity => Math.Max(0, RequiredQuantity - PurchasedQuantity);
        public double CompletionPercentage => RequiredQuantity > 0 ? (double)PurchasedQuantity / RequiredQuantity * 100 : 0;
        public bool IsCompleted => PurchasedQuantity >= RequiredQuantity;
        public decimal TotalValue => PurchasePrice * Quantity;

        /// <summary>
        /// Creates an EnrichedTransactionData from a PurchaseTransaction and KeywordSnapshot
        /// </summary>
        /// <param name="transaction">The purchase transaction</param>
        /// <param name="keywordSnapshot">The keyword snapshot</param>
        /// <returns>Enriched transaction data</returns>
        public static EnrichedTransactionData FromTransactionAndKeyword(PurchaseTransaction transaction, KeywordSnapshot keywordSnapshot)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction));

            var enriched = new EnrichedTransactionData
            {
                // Copy transaction properties
                Id = transaction.Id,
                KeywordId = transaction.KeywordId,
                JobId = transaction.JobId,
                ItemId = transaction.ItemId,
                ItemTitle = transaction.ItemTitle,
                PurchasePrice = transaction.PurchasePrice,
                Quantity = transaction.Quantity,
                PurchaseDate = transaction.PurchaseDate,
                Status = transaction.Status,
                TransactionId = transaction.TransactionId,
                PaymentMethod = transaction.PaymentMethod,
                ShippingAddress = transaction.ShippingAddress,
                Notes = transaction.Notes
            };

            // Copy keyword properties if available
            if (keywordSnapshot != null)
            {
                enriched.Alias = keywordSnapshot.Alias;
                enriched.Keywords = keywordSnapshot.Keywords;
                enriched.SearchInDescription = keywordSnapshot.SearchInDescription;
                enriched.PriceMin = keywordSnapshot.PriceMin;
                enriched.PriceMax = keywordSnapshot.PriceMax;
                enriched.Categories = keywordSnapshot.Categories;
                enriched.Condition = keywordSnapshot.ConditionString;
                enriched.EbaySiteName = keywordSnapshot.EbaySiteName;
                enriched.LocatedIn = keywordSnapshot.LocatedIn;
                enriched.AvailableTo = keywordSnapshot.AvailableTo;
                enriched.Zip = keywordSnapshot.Zip;
                enriched.Sellers = keywordSnapshot.SellersString;
                enriched.SellerType = keywordSnapshot.SellerType;
                enriched.Frequency = keywordSnapshot.Frequency;
                enriched.Threads = keywordSnapshot.Threads;
                enriched.ViewName = keywordSnapshot.ViewName;
                enriched.ListingType = keywordSnapshot.ListingTypeString;
                enriched.RequiredQuantity = keywordSnapshot.RequiredQuantity;
                enriched.PurchasedQuantity = keywordSnapshot.PurchasedQuantity;
            }

            return enriched;
        }

        /// <summary>
        /// Creates an EnrichedTransactionData from a PurchaseTransaction and Keyword2Find
        /// </summary>
        /// <param name="transaction">The purchase transaction</param>
        /// <param name="keyword">The keyword object</param>
        /// <returns>Enriched transaction data</returns>
        public static EnrichedTransactionData FromTransactionAndKeyword(PurchaseTransaction transaction, Keyword2Find keyword)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction));

            var enriched = new EnrichedTransactionData
            {
                // Copy transaction properties
                Id = transaction.Id,
                KeywordId = transaction.KeywordId,
                JobId = transaction.JobId,
                ItemId = transaction.ItemId,
                ItemTitle = transaction.ItemTitle,
                PurchasePrice = transaction.PurchasePrice,
                Quantity = transaction.Quantity,
                PurchaseDate = transaction.PurchaseDate,
                Status = transaction.Status,
                TransactionId = transaction.TransactionId,
                PaymentMethod = transaction.PaymentMethod,
                ShippingAddress = transaction.ShippingAddress,
                Notes = transaction.Notes
            };

            // Copy keyword properties if available
            if (keyword != null)
            {
                enriched.Alias = keyword.Alias ?? string.Empty;
                enriched.Keywords = keyword.Kws ?? string.Empty;
                enriched.SearchInDescription = keyword.SearchInDescription;
                enriched.PriceMin = keyword.PriceMin;
                enriched.PriceMax = keyword.PriceMax;
                enriched.Categories = keyword.Categories4Api ?? string.Empty;
                enriched.Condition = keyword.Condition != null ? string.Join(", ", keyword.Condition) : string.Empty;
                enriched.EbaySiteName = keyword.EbaySiteName ?? string.Empty;
                enriched.LocatedIn = keyword.LocatedIn ?? string.Empty;
                enriched.AvailableTo = keyword.AvailableTo ?? string.Empty;
                enriched.Zip = keyword.Zip ?? string.Empty;
                enriched.Sellers = keyword.Sellers != null ? string.Join(", ", keyword.Sellers) : string.Empty;
                enriched.SellerType = keyword.SellerType ?? string.Empty;
                enriched.Frequency = keyword.Frequency;
                enriched.Threads = keyword.Threads;
                enriched.ViewName = keyword.ViewName ?? string.Empty;
                enriched.ListingType = keyword.ListingType != null ? string.Join(", ", keyword.ListingType) : string.Empty;
                enriched.RequiredQuantity = keyword.RequiredQuantity;
                enriched.PurchasedQuantity = keyword.PurchasedQuantity;
            }

            return enriched;
        }
    }
}
