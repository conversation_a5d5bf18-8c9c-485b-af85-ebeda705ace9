﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;
using uBuyFirst.Restocker.Data;

namespace uBuyFirst.Tests.Forms
{
    [TestClass]
    public class FormRestockReportEnhancedTests
    {
        private IPurchaseTrackerRepository _mockRepository;
        private IKeywordDataService _mockKeywordService;
        private IReportSnapshotStorage _mockStorage;
        private string _testDirectory;

        [TestInitialize]
        public void Setup()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "RestockReportEnhancedTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            _mockRepository = new MockPurchaseTrackerRepository();
            _mockKeywordService = new KeywordDataService(CreateTestKeywords());
            _mockStorage = new FileReportSnapshotStorage(_testDirectory);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        [TestMethod]
        public void FormRestockReport_Constructor_WithDependencyInjection_InitializesCorrectly()
        {
            // Act
            var form = new FormRestockReport(_mockRepository, _mockKeywordService, _mockStorage);

            // Assert
            Assert.IsNotNull(form);
            // Form should be initialized without throwing exceptions
        }

        [TestMethod]
        public void FormRestockReport_DefaultConstructor_InitializesWithDefaults()
        {
            // Act & Assert - Should not throw
            var form = new FormRestockReport();
            Assert.IsNotNull(form);
        }

        [TestMethod]
        public async Task CreateEnrichedTransactionData_WithValidData_ReturnsEnrichedData()
        {
            // Arrange
            var transactions = CreateTestTransactions();
            var form = new FormRestockReport(_mockRepository, _mockKeywordService, _mockStorage);

            // Use reflection to access private method for testing
            var method = typeof(FormRestockReport).GetMethod("CreateEnrichedTransactionDataAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = await (Task<List<EnrichedTransactionData>>)method.Invoke(form, new object[] { transactions });

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);

            var firstEnriched = result.First();
            Assert.AreEqual("Test Product 1", firstEnriched.Alias);
            Assert.AreEqual("test,product,keywords", firstEnriched.Keywords);
            Assert.AreEqual(5, firstEnriched.RequiredQuantity);
            Assert.AreEqual(2, firstEnriched.PurchasedQuantity);
        }

        [TestMethod]
        public async Task CreateCompleteReportSnapshot_WithValidData_CreatesSnapshot()
        {
            // Arrange
            var transactions = CreateTestTransactions();
            var filter = new ReportFilter
            {
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now,
                IncludeCompleted = true
            };

            var form = new FormRestockReport(_mockRepository, _mockKeywordService, _mockStorage);

            // Set up the form's internal state
            var transactionsField = typeof(FormRestockReport).GetField("_currentTransactions",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            transactionsField.SetValue(form, transactions);

            // Use reflection to access private method
            var method = typeof(FormRestockReport).GetMethod("CreateCompleteReportSnapshotAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = await (Task<ReportSnapshot>)method.Invoke(form, new object[] { filter });

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.ReportId);
            Assert.AreEqual(filter.StartDate, result.Filter.StartDate);
            Assert.AreEqual(filter.EndDate, result.Filter.EndDate);
            Assert.AreEqual(2, result.Data.Count);

            // Verify snapshot contains keyword data
            var firstItem = result.Data.First();
            Assert.IsNotNull(firstItem.KeywordState);
            Assert.AreEqual("Test Product 1", firstItem.KeywordState.Alias);
        }

        [TestMethod]
        public async Task ExportReportSnapshotToCsv_WithValidSnapshot_CreatesCorrectCsvFile()
        {
            // Arrange
            var snapshot = CreateTestReportSnapshot();
            var csvFilePath = Path.Combine(_testDirectory, "test_export.csv");

            var form = new FormRestockReport(_mockRepository, _mockKeywordService, _mockStorage);

            // Use reflection to access private method
            var method = typeof(FormRestockReport).GetMethod("ExportReportSnapshotToCsvAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            await (Task)method.Invoke(form, new object[] { snapshot, csvFilePath });

            // Assert
            Assert.IsTrue(File.Exists(csvFilePath));

            var csvContent = File.ReadAllText(csvFilePath);
            Assert.IsTrue(csvContent.Contains("Generated At"));
            Assert.IsTrue(csvContent.Contains("Alias"));
            Assert.IsTrue(csvContent.Contains("Keywords"));
            Assert.IsTrue(csvContent.Contains("Required Quantity"));
            Assert.IsTrue(csvContent.Contains("Test Product 1"));
            Assert.IsTrue(csvContent.Contains("test,product,keywords"));

            // Verify it has multiple lines (header + data)
            var lines = csvContent.Split(new[]{ '\n' }, StringSplitOptions.RemoveEmptyEntries);
            Assert.IsTrue(lines.Length >= 2);
        }

        [TestMethod]
        public async Task CaptureGridColumnState_ReturnsColumnConfiguration()
        {
            // Arrange
            var form = new FormRestockReport(_mockRepository, _mockKeywordService, _mockStorage);

            // Use reflection to access private method
            var method = typeof(FormRestockReport).GetMethod("CaptureGridColumnState",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = (List<GridColumnSnapshot>)method.Invoke(form, new object[0]);

            // Assert
            Assert.IsNotNull(result);
            // Should return empty list if grid is not properly initialized in test environment
            // In real usage, this would capture actual column configuration
        }

        private List<PurchaseTransaction> CreateTestTransactions()
        {
            return new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    ItemId = "item-1",
                    ItemTitle = "Test Item 1",
                    PurchasePrice = 25.99m,
                    Quantity = 1,
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-1)
                },
                new PurchaseTransaction
                {
                    Id = 2,
                    KeywordId = "keyword-2",
                    JobId = "job-456",
                    ItemId = "item-2",
                    ItemTitle = "Test Item 2",
                    PurchasePrice = 45.50m,
                    Quantity = 2,
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-2)
                }
            };
        }

        private List<Keyword2Find> CreateTestKeywords()
        {
            return new List<Keyword2Find>
            {
                new Keyword2Find
                {
                    Id = "keyword-1",
                    Alias = "Test Product 1",
                    Kws = "test,product,keywords",
                    JobId = "job-123",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 2,
                    PriceMin = 10.0,
                    PriceMax = 50.0
                },
                new Keyword2Find
                {
                    Id = "keyword-2",
                    Alias = "Test Product 2",
                    Kws = "another,test,product",
                    JobId = "job-456",
                    RequiredQuantity = 3,
                    PurchasedQuantity = 1,
                    PriceMin = 20.0,
                    PriceMax = 100.0
                }
            };
        }

        private ReportSnapshot CreateTestReportSnapshot()
        {
            return new ReportSnapshot
            {
                ReportId = "test-report-123",
                GeneratedAt = new DateTime(2024, 12, 19, 14, 30, 0),
                Filter = new ReportFilter
                {
                    StartDate = new DateTime(2024, 11, 19),
                    EndDate = new DateTime(2024, 12, 19),
                    IncludeCompleted = true
                },
                Data = new List<TransactionWithKeywordSnapshot>
                {
                    new TransactionWithKeywordSnapshot
                    {
                        Transaction = new PurchaseTransaction
                        {
                            Id = 1,
                            KeywordId = "keyword-1",
                            ItemTitle = "Test Item 1",
                            PurchasePrice = 25.99m,
                            Quantity = 1,
                            Status = "Completed"
                        },
                        KeywordState = new KeywordSnapshot
                        {
                            KeywordId = "keyword-1",
                            Alias = "Test Product 1",
                            Keywords = "test,product,keywords",
                            RequiredQuantity = 5,
                            PurchasedQuantity = 2
                        }
                    }
                }
            };
        }
    }

    // Mock repository for testing
    public class MockPurchaseTrackerRepository : IPurchaseTrackerRepository
    {
        public Task<IEnumerable<PurchaseTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "keyword-1",
                    ItemTitle = "Mock Item 1",
                    PurchasePrice = 25.99m,
                    Quantity = 1,
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-1)
                }
            };
            return Task.FromResult<IEnumerable<PurchaseTransaction>>(transactions);
        }

        // Implement all required interface methods with correct return types
        public Task InitializeDatabaseAsync() => Task.CompletedTask;

        public Task<int> GetPurchasedQuantityAsync(string keywordId, string jobId) => Task.FromResult(0);

        public Task<IEnumerable<PurchaseTransaction>> GetTransactionsByKeywordAndJobAsync(string keywordId, string jobId) =>
            Task.FromResult<IEnumerable<PurchaseTransaction>>(new List<PurchaseTransaction>());

        public Task<int> AddTransactionAsync(PurchaseTransaction transaction) => Task.FromResult(1);

        public Task<bool> UpdateTransactionStatusAsync(int transactionId, string status) => Task.FromResult(true);

        public Task<IEnumerable<PurchaseAttempt>> GetAttemptsByKeywordAndJobAsync(string keywordId, string jobId) =>
            Task.FromResult<IEnumerable<PurchaseAttempt>>(new List<PurchaseAttempt>());

        public Task<int> AddAttemptAsync(PurchaseAttempt attempt) => Task.FromResult(1);

        public Task<IEnumerable<SyncHistory>> GetSyncHistoryAsync(int limit = 50) =>
            Task.FromResult<IEnumerable<SyncHistory>>(new List<SyncHistory>());

        public Task<int> AddSyncHistoryAsync(SyncHistory syncHistory) => Task.FromResult(1);

        public Task<string> GetConfigurationValueAsync(string key) => Task.FromResult(string.Empty);

        public Task SetConfigurationValueAsync(string key, string value) => Task.CompletedTask;

        public Task<IEnumerable<PurchaseTransaction>> GetAllTransactionsAsync() =>
            Task.FromResult<IEnumerable<PurchaseTransaction>>(new List<PurchaseTransaction>());

        public void Dispose() { }
    }
}
