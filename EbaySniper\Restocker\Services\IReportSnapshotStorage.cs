using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Interface for storing and retrieving report snapshots
    /// </summary>
    public interface IReportSnapshotStorage
    {
        /// <summary>
        /// Saves a report snapshot to storage
        /// </summary>
        /// <param name="snapshot">The report snapshot to save</param>
        /// <returns>Task representing the async operation</returns>
        Task SaveSnapshotAsync(ReportSnapshot snapshot);

        /// <summary>
        /// Loads a report snapshot by its ID
        /// </summary>
        /// <param name="reportId">The unique identifier of the report</param>
        /// <returns>The report snapshot if found, null otherwise</returns>
        Task<ReportSnapshot> LoadSnapshotAsync(string reportId);

        /// <summary>
        /// Gets all available report snapshots, ordered by generation date (newest first)
        /// </summary>
        /// <returns>List of all report snapshots</returns>
        Task<List<ReportSnapshot>> GetAllSnapshotsAsync();

        /// <summary>
        /// Deletes a report snapshot by its ID
        /// </summary>
        /// <param name="reportId">The unique identifier of the report to delete</param>
        /// <returns>True if the snapshot was deleted, false if it wasn't found</returns>
        Task<bool> DeleteSnapshotAsync(string reportId);

        /// <summary>
        /// Gets the storage directory path where snapshots are stored
        /// </summary>
        /// <returns>The full path to the storage directory</returns>
        string GetStorageDirectory();
    }
}
