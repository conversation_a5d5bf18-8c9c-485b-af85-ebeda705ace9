using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Forms
{
    [TestClass]
    [TestCategory("FormRestockReport")]
    public class FormRestockReportTests
    {
        private Mock<IPurchaseTrackerRepository> _mockRepository;
        private Mock<IReportService> _mockReportService;
        private Mock<IReportExportService> _mockExportService;
        private List<PurchaseTransaction> _testTransactions;

        [TestInitialize]
        public void Setup()
        {
            _mockRepository = new Mock<IPurchaseTrackerRepository>();
            _mockReportService = new Mock<IReportService>();
            _mockExportService = new Mock<IReportExportService>();

            // Setup test data
            _testTransactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    ItemId = "item-456",
                    ItemTitle = "Test Product 1",
                    PurchasePrice = 25.99m,
                    Quantity = 2,
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-5)
                },
                new PurchaseTransaction
                {
                    Id = 2,
                    KeywordId = "keyword-2",
                    JobId = "job-456",
                    ItemId = "item-789",
                    ItemTitle = "Test Product 2",
                    PurchasePrice = 15.50m,
                    Quantity = 1,
                    Status = "Failed",
                    PurchaseDate = DateTime.Now.AddDays(-3)
                },
                new PurchaseTransaction
                {
                    Id = 3,
                    KeywordId = "keyword-3",
                    JobId = "job-789",
                    ItemId = "item-101",
                    ItemTitle = "Test Product 3",
                    PurchasePrice = 45.00m,
                    Quantity = 3,
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-1)
                }
            };
        }

        [TestMethod]
        public void FormRestockReport_Constructor_InitializesCorrectly()
        {
            // Act
            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);

            // Assert
            Assert.IsNotNull(form);
            Assert.AreEqual(30, form.GetDaysValue(), "Default days should be 30");
            Assert.IsFalse(form.IsExportButtonEnabled(), "Export button should be disabled initially");
            Assert.IsTrue(form.IsGenerateButtonEnabled(), "Generate button should be enabled initially");
        }

        [TestMethod]
        public async Task GenerateReport_WithValidData_PopulatesGridAndEnablesExport()
        {
            // Arrange
            var startDate = DateTime.Now.AddDays(-30);
            var endDate = DateTime.Now;

            _mockRepository.Setup(r => r.GetTransactionsByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                          .ReturnsAsync(_testTransactions);

            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);

            // Act
            await form.GenerateReportAsync();

            // Assert
            Assert.AreEqual(3, form.GetTransactionCount(), "Should have 3 transactions");
            Assert.IsTrue(form.IsExportButtonEnabled(), "Export button should be enabled after successful generation");
            Assert.IsTrue(form.GetStatusText().Contains("Report generated successfully"), "Status should indicate success");

            // Verify repository was called with correct date range
            _mockRepository.Verify(r => r.GetTransactionsByDateRangeAsync(
                It.Is<DateTime>(d => d.Date == DateTime.Now.AddDays(-30).Date),
                It.Is<DateTime>(d => d.Date == DateTime.Now.Date)), Times.Once);
        }

        [TestMethod]
        public async Task GenerateReport_WithNoData_ShowsEmptyResults()
        {
            // Arrange
            _mockRepository.Setup(r => r.GetTransactionsByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                          .ReturnsAsync(new List<PurchaseTransaction>());

            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);

            // Act
            await form.GenerateReportAsync();

            // Assert
            Assert.AreEqual(0, form.GetTransactionCount(), "Should have 0 transactions");
            Assert.IsFalse(form.IsExportButtonEnabled(), "Export button should remain disabled with no data");
            Assert.IsTrue(form.GetStatusText().Contains("Found 0 transactions"), "Status should show 0 transactions");
        }

        [TestMethod]
        public void UpdateSummary_WithMixedTransactions_CalculatesCorrectly()
        {
            // Arrange
            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(_testTransactions);

            // Act
            form.UpdateSummaryPublic();

            // Assert
            var summary = form.GetSummaryValues();
            Assert.AreEqual(3, summary.TotalTransactions, "Should count all transactions");
            Assert.AreEqual(2, summary.CompletedTransactions, "Should count completed transactions");
            Assert.AreEqual(1, summary.FailedTransactions, "Should count failed transactions");
            
            // Total amount should be sum of completed transactions: (25.99 * 2) + (45.00 * 3) = 186.98
            Assert.AreEqual(186.98m, summary.TotalAmount, "Should calculate total amount for completed transactions only");
        }

        [TestMethod]
        public void UpdateSummary_WithEmptyData_ShowsZeros()
        {
            // Arrange
            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(new List<PurchaseTransaction>());

            // Act
            form.UpdateSummaryPublic();

            // Assert
            var summary = form.GetSummaryValues();
            Assert.AreEqual(0, summary.TotalTransactions);
            Assert.AreEqual(0, summary.CompletedTransactions);
            Assert.AreEqual(0, summary.FailedTransactions);
            Assert.AreEqual(0m, summary.TotalAmount);
        }

        [TestMethod]
        public async Task ExportReport_WithValidData_CallsExportService()
        {
            // Arrange
            _mockExportService.Setup(s => s.ExportTransactionDetailReportToCsvAsync(It.IsAny<TransactionDetailReport>(), It.IsAny<string>()))
                             .Returns(Task.CompletedTask);

            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(_testTransactions);

            // Act
            await form.ExportReportAsync("test_report.csv");

            // Assert
            _mockExportService.Verify(s => s.ExportTransactionDetailReportToCsvAsync(
                It.Is<TransactionDetailReport>(r => 
                    r.KeywordId == "All" && 
                    r.JobId == "All" && 
                    r.KeywordAlias == "All Restock Transactions" &&
                    r.Transactions.Count == 3), 
                "test_report.csv"), Times.Once);
        }

        [TestMethod]
        public async Task ExportReport_WithNoData_ShowsWarningMessage()
        {
            // Arrange
            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(new List<PurchaseTransaction>());

            // Act
            var result = await form.ExportReportAsync("test_report.csv");

            // Assert
            Assert.IsFalse(result, "Export should return false when no data");
            _mockExportService.Verify(s => s.ExportTransactionDetailReportToCsvAsync(It.IsAny<TransactionDetailReport>(), It.IsAny<string>()), 
                Times.Never, "Export service should not be called with no data");
        }

        [TestMethod]
        public void SetDaysValue_UpdatesSpinEditAndClearsStatus()
        {
            // Arrange
            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(_testTransactions); // Simulate having data

            // Act
            form.SetDaysValue(60);

            // Assert
            Assert.AreEqual(60, form.GetDaysValue(), "Days value should be updated");
            Assert.IsTrue(form.GetStatusText().Contains("Days value changed"), "Status should indicate days changed");
        }

        [TestMethod]
        public async Task GenerateReport_WithRepositoryError_ShowsErrorMessage()
        {
            // Arrange
            _mockRepository.Setup(r => r.GetTransactionsByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                          .ThrowsAsync(new Exception("Database connection failed"));

            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);

            // Act
            await form.GenerateReportAsync();

            // Assert
            Assert.IsTrue(form.GetStatusText().Contains("Error generating report"), "Status should show error message");
            Assert.IsTrue(form.IsGenerateButtonEnabled(), "Generate button should be re-enabled after error");
            Assert.IsFalse(form.IsExportButtonEnabled(), "Export button should remain disabled after error");
        }

        [TestMethod]
        public async Task ExportReport_WithExportServiceError_ShowsErrorMessage()
        {
            // Arrange
            _mockExportService.Setup(s => s.ExportTransactionDetailReportToCsvAsync(It.IsAny<TransactionDetailReport>(), It.IsAny<string>()))
                             .ThrowsAsync(new Exception("File access denied"));

            using var form = new TestableFormRestockReport(_mockRepository.Object, _mockReportService.Object, _mockExportService.Object);
            form.SetTestTransactions(_testTransactions);

            // Act
            await form.ExportReportAsync("test_report.csv");

            // Assert
            Assert.IsTrue(form.GetStatusText().Contains("Error exporting report"), "Status should show export error");
            Assert.IsTrue(form.IsExportButtonEnabled(), "Export button should be re-enabled after error");
        }

        public struct SummaryValues
        {
            public int TotalTransactions;
            public int CompletedTransactions;
            public int FailedTransactions;
            public decimal TotalAmount;
        }
    }
}
