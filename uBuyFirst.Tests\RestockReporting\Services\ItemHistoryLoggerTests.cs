using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.RestockReporting.Services
{
    [TestClass]
    public class ItemHistoryLoggerTests
    {
        private FileItemHistoryLogger _logger;
        private string _testBasePath;
        private string _testErrorPath;

        [TestInitialize]
        public void Setup()
        {
            // Create temporary test directories
            _testBasePath = Path.Combine(Path.GetTempPath(), "ItemHistoryTests", Guid.NewGuid().ToString());
            _testErrorPath = Path.Combine(_testBasePath, "errors");

            Directory.CreateDirectory(_testBasePath);
            Directory.CreateDirectory(_testErrorPath);

            var options = new ItemHistoryOptions
            {
                BasePath = _testBasePath,
                ErrorLogPath = _testErrorPath,
                EnableLogging = true
            };

            _logger = new FileItemHistoryLogger(options);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _logger?.Dispose();

            // Clean up test directories
            if (Directory.Exists(_testBasePath))
            {
                Directory.Delete(_testBasePath, true);
            }
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithValidContext_CreatesJsonFile()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();

            // Act
            await _logger.LogItemProcessingAsync(context);

            // Assert
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            Assert.IsTrue(Directory.Exists(todayFolder), "Daily folder should be created");

            var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create exactly one JSON file");

            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"itemId\": \"123456789\""), "JSON should contain item ID");
            Assert.IsTrue(jsonContent.Contains("\"outcome\": \"purchased\""), "JSON should contain outcome");
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithSameContext_OverwritesFile()
        {
            // Arrange
            var context1 = CreateTestItemProcessingContext();
            var context2 = CreateTestItemProcessingContext();
            context2.Reason = "Updated reason"; // Change content to verify overwrite

            // Act
            await _logger.LogItemProcessingAsync(context1);
            await _logger.LogItemProcessingAsync(context2);

            // Assert
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create only one JSON file (overwritten)");

            // Verify the file contains the updated content
            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"reason\": \"Updated reason\""), "File should contain updated content");
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithNullContext_DoesNotThrow()
        {
            // Act & Assert - Should not throw
            await _logger.LogItemProcessingAsync(null);
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithInvalidPath_LogsErrorButContinues()
        {
            // Arrange
            var invalidOptions = new ItemHistoryOptions
            {
                BasePath = "Z:\\InvalidPath\\That\\Does\\Not\\Exist",
                ErrorLogPath = _testErrorPath,
                EnableLogging = true
            };
            var invalidLogger = new FileItemHistoryLogger(invalidOptions);
            var context = CreateTestItemProcessingContext();

            // Act - Should not throw
            await invalidLogger.LogItemProcessingAsync(context);

            // Assert - Error should be logged
            var errorFiles = Directory.GetFiles(_testErrorPath, "*_errors.log");
            Assert.IsTrue(errorFiles.Length > 0, "Should create error log file");

            var errorContent = File.ReadAllText(errorFiles[0]);
            Assert.IsTrue(errorContent.Contains("Failed to write item history"), "Should log the error");
        }

        [TestMethod]
        public void FileItemHistoryLogger_WithDisabledLogging_DoesNotLog()
        {
            // Arrange
            var disabledOptions = new ItemHistoryOptions
            {
                BasePath = _testBasePath,
                ErrorLogPath = _testErrorPath,
                EnableLogging = false
            };
            var disabledLogger = new FileItemHistoryLogger(disabledOptions);
            var context = CreateTestItemProcessingContext();

            // Act
            var task = disabledLogger.LogItemProcessingAsync(context);

            // Assert
            Assert.IsTrue(task.IsCompleted, "Should complete immediately when disabled");

            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            Assert.IsFalse(Directory.Exists(todayFolder), "Should not create folder when disabled");
        }

        [TestMethod]
        public void GenerateFileName_WithValidContext_CreatesCorrectFormat()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();

            // Act
            var fileName = _logger.GenerateFileName(context);

            // Assert
            Assert.AreEqual("123456789_JOB-001_kw-test-123.json", fileName, "Should follow format: ItemId_JobId_KeywordId.json");
        }

        private ItemProcessingContext CreateTestItemProcessingContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "purchased",
                Reason = "Successfully purchased 2 items",
                ItemData = new ItemHistoryData
                {
                    ItemId = "123456789",
                    Title = "Test Item",
                    CurrentPrice = 25.99m,
                    Condition = "New",
                    Seller = "test-seller"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-test-123",
                    Alias = "Test Keyword",
                    JobId = "JOB-001",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 2
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Filter",
                    Expression = "Price <= 40",
                    Matched = true,
                    EvaluationResult = "Filter matched"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    TransactionId = "txn-123",
                    PurchasePrice = 25.99m,
                    Quantity = 2
                }
            };
        }
    }
}
