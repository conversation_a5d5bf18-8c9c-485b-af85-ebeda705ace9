using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Forms
{
    /// <summary>
    /// Testable version of FormRestockReport that exposes internal methods and properties for unit testing
    /// </summary>
    public class TestableFormRestockReport : IDisposable
    {
        private readonly IPurchaseTrackerRepository _repository;
        private readonly IReportService _reportService;
        private readonly IReportExportService _exportService;
        private List<PurchaseTransaction> _currentTransactions;
        
        // Mock UI controls
        private SpinEdit _spinEditDays;
        private SimpleButton _btnGenerateReport;
        private SimpleButton _btnExport;
        private GridControl _gridControlTransactions;
        private LabelControl _lblStatus;
        private LabelControl _lblTotalTransactions;
        private LabelControl _lblTotalAmount;
        private LabelControl _lblCompletedTransactions;
        private LabelControl _lblFailedTransactions;

        public TestableFormRestockReport(IPurchaseTrackerRepository repository, IReportService reportService, IReportExportService exportService)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _exportService = exportService ?? throw new ArgumentNullException(nameof(exportService));
            
            InitializeMockControls();
            _currentTransactions = new List<PurchaseTransaction>();
        }

        private void InitializeMockControls()
        {
            _spinEditDays = new SpinEdit { Value = 30 };
            _btnGenerateReport = new SimpleButton { Enabled = true };
            _btnExport = new SimpleButton { Enabled = false };
            _gridControlTransactions = new GridControl();
            _lblStatus = new LabelControl { Text = "Ready" };
            _lblTotalTransactions = new LabelControl { Text = "Total Transactions: 0" };
            _lblTotalAmount = new LabelControl { Text = "Total Amount: $0.00" };
            _lblCompletedTransactions = new LabelControl { Text = "Completed: 0" };
            _lblFailedTransactions = new LabelControl { Text = "Failed: 0" };
        }

        // Public methods for testing
        public async Task GenerateReportAsync()
        {
            try
            {
                _btnGenerateReport.Enabled = false;
                _btnExport.Enabled = false;
                
                _lblStatus.Text = "Generating report...";
                
                var days = (int)_spinEditDays.Value;
                var startDate = DateTime.Now.AddDays(-days);
                var endDate = DateTime.Now;
                
                // Get transactions
                _currentTransactions = (await _repository.GetTransactionsByDateRangeAsync(startDate, endDate)).ToList();
                
                // Bind to grid (simulated)
                _gridControlTransactions.DataSource = _currentTransactions;
                
                // Update summary
                UpdateSummaryPublic();
                
                _lblStatus.Text = $"Report generated successfully. Found {_currentTransactions.Count} transactions.";
                _btnExport.Enabled = _currentTransactions.Count > 0;
            }
            catch (Exception ex)
            {
                _lblStatus.Text = $"Error generating report: {ex.Message}";
            }
            finally
            {
                _btnGenerateReport.Enabled = true;
            }
        }

        public async Task<bool> ExportReportAsync(string filePath)
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                return false; // Simulate showing warning message
            }

            try
            {
                _btnExport.Enabled = false;
                _lblStatus.Text = "Exporting report...";

                // Create a simple transaction report for export
                var report = new TransactionDetailReport
                {
                    KeywordId = "All",
                    JobId = "All",
                    KeywordAlias = "All Restock Transactions",
                    GeneratedAt = DateTime.Now,
                    Transactions = _currentTransactions,
                    Attempts = new List<PurchaseAttempt>()
                };

                await _exportService.ExportTransactionDetailReportToCsvAsync(report, filePath);
                
                _lblStatus.Text = $"Report exported successfully to: {filePath}";
                return true;
            }
            catch (Exception ex)
            {
                _lblStatus.Text = $"Error exporting report: {ex.Message}";
                return false;
            }
            finally
            {
                _btnExport.Enabled = true;
            }
        }

        public void UpdateSummaryPublic()
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                _lblTotalTransactions.Text = "Total Transactions: 0";
                _lblTotalAmount.Text = "Total Amount: $0.00";
                _lblCompletedTransactions.Text = "Completed: 0";
                _lblFailedTransactions.Text = "Failed: 0";
                return;
            }

            var totalTransactions = _currentTransactions.Count;
            var totalAmount = _currentTransactions.Where(t => t.Status == "Completed").Sum(t => t.PurchasePrice * t.Quantity);
            var completedCount = _currentTransactions.Count(t => t.Status == "Completed");
            var failedCount = _currentTransactions.Count(t => t.Status == "Failed");

            _lblTotalTransactions.Text = $"Total Transactions: {totalTransactions}";
            _lblTotalAmount.Text = $"Total Amount: ${totalAmount:F2}";
            _lblCompletedTransactions.Text = $"Completed: {completedCount}";
            _lblFailedTransactions.Text = $"Failed: {failedCount}";
        }

        // Test helper methods
        public void SetTestTransactions(List<PurchaseTransaction> transactions)
        {
            _currentTransactions = transactions ?? new List<PurchaseTransaction>();
            _gridControlTransactions.DataSource = _currentTransactions;
        }

        public void SetDaysValue(int days)
        {
            _spinEditDays.Value = days;
            
            // Simulate the EditValueChanged event behavior
            if (_currentTransactions?.Any() == true)
            {
                _lblStatus.Text = "Days value changed. Click Generate Report to refresh data.";
            }
        }

        // Property accessors for testing
        public int GetDaysValue() => (int)_spinEditDays.Value;
        public bool IsGenerateButtonEnabled() => _btnGenerateReport.Enabled;
        public bool IsExportButtonEnabled() => _btnExport.Enabled;
        public string GetStatusText() => _lblStatus.Text;
        public int GetTransactionCount() => _currentTransactions?.Count ?? 0;

        public FormRestockReportTests.SummaryValues GetSummaryValues()
        {
            return new FormRestockReportTests.SummaryValues
            {
                TotalTransactions = ExtractNumberFromText(_lblTotalTransactions.Text, "Total Transactions: "),
                CompletedTransactions = ExtractNumberFromText(_lblCompletedTransactions.Text, "Completed: "),
                FailedTransactions = ExtractNumberFromText(_lblFailedTransactions.Text, "Failed: "),
                TotalAmount = ExtractAmountFromText(_lblTotalAmount.Text, "Total Amount: $")
            };
        }

        private int ExtractNumberFromText(string text, string prefix)
        {
            if (text.StartsWith(prefix))
            {
                var numberPart = text.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int result))
                    return result;
            }
            return 0;
        }

        private decimal ExtractAmountFromText(string text, string prefix)
        {
            if (text.StartsWith(prefix))
            {
                var amountPart = text.Substring(prefix.Length);
                if (decimal.TryParse(amountPart, out decimal result))
                    return result;
            }
            return 0m;
        }

        public void Dispose()
        {
            _spinEditDays?.Dispose();
            _btnGenerateReport?.Dispose();
            _btnExport?.Dispose();
            _gridControlTransactions?.Dispose();
            _lblStatus?.Dispose();
            _lblTotalTransactions?.Dispose();
            _lblTotalAmount?.Dispose();
            _lblCompletedTransactions?.Dispose();
            _lblFailedTransactions?.Dispose();
            _repository?.Dispose();
        }
    }
}
