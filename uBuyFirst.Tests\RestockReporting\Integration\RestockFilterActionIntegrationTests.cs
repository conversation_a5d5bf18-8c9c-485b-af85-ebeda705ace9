using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.RestockReporting.Integration
{
    [TestClass]
    public class RestockFilterActionIntegrationTests
    {
        private RestockFilterAction _action;
        private string _testBasePath;
        private Mock<IPurchaseTrackerRepository> _mockRepository;

        [TestInitialize]
        public void Setup()
        {
            // Create temporary test directories
            _testBasePath = Path.Combine(Path.GetTempPath(), "RestockFilterActionIntegrationTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testBasePath);

            // Setup mock repository
            _mockRepository = new Mock<IPurchaseTrackerRepository>();

            // Create the action
            _action = new RestockFilterAction();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _action?.Dispose();

            // Clean up test directories
            if (Directory.Exists(_testBasePath))
            {
                Directory.Delete(_testBasePath, true);
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithMatchingItems_LogsItemProcessingHistory()
        {
            // Arrange
            var context = CreateTestFilterActionContext();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed");

            // Verify that JSON files were created
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                Assert.IsTrue(jsonFiles.Length > 0, "Should create JSON history files");
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithNonMatchingItems_LogsFilteredOutItems()
        {
            // Arrange
            var context = CreateTestFilterActionContextWithNonMatchingItems();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed even with no matches");

            // Verify that JSON files were created for filtered out items
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                Assert.IsTrue(jsonFiles.Length > 0, "Should create JSON history files for filtered out items");
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithPurchaseDisabled_LogsSkippedPurchases()
        {
            // Arrange
            _action.EnablePurchasing = false;
            var context = CreateTestFilterActionContext();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed");

            // Verify that items were logged as skipped
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                if (jsonFiles.Length > 0)
                {
                    var jsonContent = File.ReadAllText(jsonFiles[0]);
                    Assert.IsTrue(jsonContent.Contains("\"outcome\": \"skipped\"") ||
                                jsonContent.Contains("Purchase execution is disabled"),
                                "Should log purchase as skipped when purchasing is disabled");
                }
            }
        }

        private IFilterActionContext CreateTestFilterActionContext()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Blob", typeof(DataList));
            dataTable.Columns.Add("ItemID", typeof(string));
            dataTable.Columns.Add("Title", typeof(string));
            dataTable.Columns.Add("Price", typeof(decimal));

            // Add test data
            var testDataList = CreateTestDataList();
            var row = dataTable.NewRow();
            row["Blob"] = testDataList;
            row["ItemID"] = testDataList.ItemID;
            row["Title"] = testDataList.Title;
            row["Price"] = testDataList.ItemPrice;
            dataTable.Rows.Add(row);

            var filter = new XFilterClass
            {
                Alias = "Test Restock Filter",
                Expression = "Price <= 100", // This should match our test data
                Enabled = true
            };

            return new FilterActionContext
            {
                FilterRule = filter,
                SourceDataTable = dataTable
            };
        }

        private IFilterActionContext CreateTestFilterActionContextWithNonMatchingItems()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Blob", typeof(DataList));
            dataTable.Columns.Add("ItemID", typeof(string));
            dataTable.Columns.Add("Title", typeof(string));
            dataTable.Columns.Add("Price", typeof(decimal));

            // Add test data
            var testDataList = CreateTestDataList();
            var row = dataTable.NewRow();
            row["Blob"] = testDataList;
            row["ItemID"] = testDataList.ItemID;
            row["Title"] = testDataList.Title;
            row["Price"] = testDataList.ItemPrice;
            dataTable.Rows.Add(row);

            var filter = new XFilterClass
            {
                Alias = "Test Restock Filter",
                Expression = "Price <= 10", // This should NOT match our test data (price is 25.99)
                Enabled = true
            };

            return new FilterActionContext
            {
                FilterRule = filter,
                SourceDataTable = dataTable
            };
        }

        private DataList CreateTestDataList()
        {
            return new DataList
            {
                ItemID = "123456789",
                Title = "Test iPhone Case",
                ItemPrice = 25.99,
                Condition = "New",
                SellerName = "test-seller",
                Shipping = 5.99,
                Location = "United States",
                QuantityAvailable = 5,
                Term = "Test Keyword Alias", // This should match a keyword
                // ViewItemURL is not a property, use GetAffiliateLink() method instead
                GalleryUrl = "https://i.ebayimg.com/test.jpg"
            };
        }
    }

    /// <summary>
    /// Mock implementation of FilterActionContext for testing
    /// </summary>
    public class FilterActionContext : IFilterActionContext
    {
        public XFilterClass FilterRule { get; set; }
        public DevExpress.XtraGrid.Views.Grid.GridView GridView { get; set; }
        public DataTable SourceDataTable { get; set; }
        public DataRow CurrentRow { get; set; }
    }
}
