﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// File-based implementation of report snapshot storage using JSON files
    /// </summary>
    public class FileReportSnapshotStorage : IReportSnapshotStorage
    {
        private readonly string _storageDirectory;
        private readonly JsonSerializerSettings _jsonSettings;

        /// <summary>
        /// Initializes a new instance of FileReportSnapshotStorage
        /// </summary>
        /// <param name="storageDirectory">Directory where JSON files will be stored</param>
        /// <exception cref="ArgumentNullException">Thrown when storageDirectory is null or empty</exception>
        public FileReportSnapshotStorage(string storageDirectory)
        {
            if (string.IsNullOrEmpty(storageDirectory))
                throw new ArgumentNullException(nameof(storageDirectory));

            _storageDirectory = storageDirectory;
            _jsonSettings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore,
                DateFormatHandling = DateFormatHandling.IsoDateFormat
            };
        }

        /// <summary>
        /// Saves a report snapshot to a JSON file
        /// </summary>
        /// <param name="snapshot">The report snapshot to save</param>
        /// <returns>Task representing the async operation</returns>
        /// <exception cref="ArgumentNullException">Thrown when snapshot is null</exception>
        public async Task SaveSnapshotAsync(ReportSnapshot snapshot)
        {
            if (snapshot == null)
                throw new ArgumentNullException(nameof(snapshot));

            // Ensure directory exists
            if (!Directory.Exists(_storageDirectory))
            {
                Directory.CreateDirectory(_storageDirectory);
            }

            // Create filename with timestamp and report ID
            var fileName = $"RestockReport_{snapshot.GeneratedAt:yyyyMMdd_HHmmss}_{snapshot.ReportId}.json";
            var filePath = Path.Combine(_storageDirectory, fileName);

            // Serialize and save
            var json = JsonConvert.SerializeObject(snapshot, _jsonSettings);
            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// Loads a report snapshot by its ID
        /// </summary>
        /// <param name="reportId">The unique identifier of the report</param>
        /// <returns>The report snapshot if found, null otherwise</returns>
        /// <exception cref="ArgumentNullException">Thrown when reportId is null or empty</exception>
        public async Task<ReportSnapshot> LoadSnapshotAsync(string reportId)
        {
            if (string.IsNullOrEmpty(reportId))
                throw new ArgumentNullException(nameof(reportId));

            if (!Directory.Exists(_storageDirectory))
                return null;

            // Find file with matching report ID
            var files = Directory.GetFiles(_storageDirectory, "*.json");
            var matchingFile = files.FirstOrDefault(f => Path.GetFileName(f).Contains($"_{reportId}.json"));

            if (matchingFile == null)
                return null;

            try
            {
                var json = File.ReadAllText(matchingFile);
                return JsonConvert.DeserializeObject<ReportSnapshot>(json, _jsonSettings);
            }
            catch (Exception)
            {
                // If file is corrupted or can't be deserialized, return null
                return null;
            }
        }

        /// <summary>
        /// Gets all available report snapshots, ordered by generation date (newest first)
        /// </summary>
        /// <returns>List of all report snapshots</returns>
        public async Task<List<ReportSnapshot>> GetAllSnapshotsAsync()
        {
            if (!Directory.Exists(_storageDirectory))
                return new List<ReportSnapshot>();

            var snapshots = new List<ReportSnapshot>();
            var files = Directory.GetFiles(_storageDirectory, "*.json");

            foreach (var file in files)
            {
                try
                {
                    var json = File.ReadAllText(file);
                    var snapshot = JsonConvert.DeserializeObject<ReportSnapshot>(json, _jsonSettings);
                    if (snapshot != null)
                    {
                        snapshots.Add(snapshot);
                    }
                }
                catch (Exception)
                {
                    // Skip corrupted files
                    continue;
                }
            }

            // Order by generation date, newest first
            return snapshots.OrderByDescending(s => s.GeneratedAt).ToList();
        }

        /// <summary>
        /// Deletes a report snapshot by its ID
        /// </summary>
        /// <param name="reportId">The unique identifier of the report to delete</param>
        /// <returns>True if the snapshot was deleted, false if it wasn't found</returns>
        /// <exception cref="ArgumentNullException">Thrown when reportId is null or empty</exception>
        public async Task<bool> DeleteSnapshotAsync(string reportId)
        {
            if (string.IsNullOrEmpty(reportId))
                throw new ArgumentNullException(nameof(reportId));

            if (!Directory.Exists(_storageDirectory))
                return false;

            // Find file with matching report ID
            var files = Directory.GetFiles(_storageDirectory, "*.json");
            var matchingFile = files.FirstOrDefault(f => Path.GetFileName(f).Contains($"_{reportId}.json"));

            if (matchingFile == null)
                return false;

            try
            {
                File.Delete(matchingFile);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the storage directory path where snapshots are stored
        /// </summary>
        /// <returns>The full path to the storage directory</returns>
        public string GetStorageDirectory()
        {
            return _storageDirectory;
        }
    }
}
