﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.Restocker.Models
{
    [TestClass]
    public class EnrichedTransactionDataTests
    {
        [TestMethod]
        public void FromTransactionAndKeyword_WithValidData_CreatesEnrichedData()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            var keyword = CreateTestKeyword();

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Assert
            // Verify transaction properties
            Assert.AreEqual(transaction.Id, enriched.Id);
            Assert.AreEqual(transaction.KeywordId, enriched.KeywordId);
            Assert.AreEqual(transaction.ItemTitle, enriched.ItemTitle);
            Assert.AreEqual(transaction.PurchasePrice, enriched.PurchasePrice);
            Assert.AreEqual(transaction.Quantity, enriched.Quantity);
            Assert.AreEqual(transaction.Status, enriched.Status);

            // Verify keyword properties
            Assert.AreEqual(keyword.Alias, enriched.Alias);
            Assert.AreEqual(keyword.Kws, enriched.Keywords);
            Assert.AreEqual(keyword.RequiredQuantity, enriched.RequiredQuantity);
            Assert.AreEqual(keyword.PurchasedQuantity, enriched.PurchasedQuantity);
            Assert.AreEqual(keyword.PriceMin, enriched.PriceMin);
            Assert.AreEqual(keyword.PriceMax, enriched.PriceMax);
        }

        [TestMethod]
        public void FromTransactionAndKeyword_WithNullKeyword_CreatesEnrichedDataWithEmptyKeywordFields()
        {
            // Arrange
            var transaction = CreateTestTransaction();

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, (Keyword2Find)null);

            // Assert
            // Verify transaction properties are copied
            Assert.AreEqual(transaction.Id, enriched.Id);
            Assert.AreEqual(transaction.ItemTitle, enriched.ItemTitle);

            // Verify keyword properties are empty/default
            Assert.AreEqual(string.Empty, enriched.Alias);
            Assert.AreEqual(string.Empty, enriched.Keywords);
            Assert.AreEqual(0, enriched.RequiredQuantity);
            Assert.AreEqual(0, enriched.PurchasedQuantity);
        }

        [TestMethod]
        public void FromTransactionAndKeyword_WithNullTransaction_ThrowsArgumentNullException()
        {
            // Arrange
            var keyword = CreateTestKeyword();

            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() =>
                EnrichedTransactionData.FromTransactionAndKeyword(null, keyword));
        }

        [TestMethod]
        public void FromTransactionAndKeywordSnapshot_WithValidData_CreatesEnrichedData()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            var keywordSnapshot = CreateTestKeywordSnapshot();

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keywordSnapshot);

            // Assert
            // Verify transaction properties
            Assert.AreEqual(transaction.Id, enriched.Id);
            Assert.AreEqual(transaction.ItemTitle, enriched.ItemTitle);

            // Verify keyword snapshot properties
            Assert.AreEqual(keywordSnapshot.Alias, enriched.Alias);
            Assert.AreEqual(keywordSnapshot.Keywords, enriched.Keywords);
            Assert.AreEqual(keywordSnapshot.RequiredQuantity, enriched.RequiredQuantity);
            Assert.AreEqual(keywordSnapshot.PurchasedQuantity, enriched.PurchasedQuantity);
        }

        [TestMethod]
        public void CalculatedProperties_WithValidData_ReturnsCorrectValues()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            transaction.PurchasePrice = 25.50m;
            transaction.Quantity = 3;

            var keyword = CreateTestKeyword();
            keyword.RequiredQuantity = 10;
            keyword.PurchasedQuantity = 6;

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Assert
            Assert.AreEqual(4, enriched.RemainingQuantity); // 10 - 6
            Assert.AreEqual(60.0, enriched.CompletionPercentage); // 6/10 * 100
            Assert.IsFalse(enriched.IsCompleted); // 6 < 10
            Assert.AreEqual(76.50m, enriched.TotalValue); // 25.50 * 3
        }

        [TestMethod]
        public void CalculatedProperties_WithCompletedPurchase_ReturnsCorrectValues()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            var keyword = CreateTestKeyword();
            keyword.RequiredQuantity = 5;
            keyword.PurchasedQuantity = 7; // More than required

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Assert
            Assert.AreEqual(0, enriched.RemainingQuantity); // Max(0, 5-7) = 0
            Assert.AreEqual(140.0, enriched.CompletionPercentage); // 7/5 * 100
            Assert.IsTrue(enriched.IsCompleted); // 7 >= 5
        }

        [TestMethod]
        public void CalculatedProperties_WithZeroRequiredQuantity_HandlesGracefully()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            var keyword = CreateTestKeyword();
            keyword.RequiredQuantity = 0;
            keyword.PurchasedQuantity = 3;

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Assert
            Assert.AreEqual(0, enriched.RemainingQuantity);
            Assert.AreEqual(0.0, enriched.CompletionPercentage); // Avoid division by zero
            Assert.IsTrue(enriched.IsCompleted); // 3 >= 0
        }

        [TestMethod]
        public void FromTransactionAndKeyword_WithArrayProperties_FormatsCorrectly()
        {
            // Arrange
            var transaction = CreateTestTransaction();
            var keyword = CreateTestKeyword();
            keyword.Condition = new[] { "New", "Used", "Refurbished" };
            keyword.Sellers = new[] { "seller1", "seller2", "seller3" };
            keyword.ListingType = new[] { ListingType.BuyItNow, ListingType.AuctionsStartedNow };

            // Act
            var enriched = EnrichedTransactionData.FromTransactionAndKeyword(transaction, keyword);

            // Assert
            Assert.AreEqual("New, Used, Refurbished", enriched.Condition);
            Assert.AreEqual("seller1, seller2, seller3", enriched.Sellers);
            Assert.AreEqual("Auction, BuyItNow", enriched.ListingType);
        }

        private PurchaseTransaction CreateTestTransaction()
        {
            return new PurchaseTransaction
            {
                Id = 1,
                KeywordId = "keyword-123",
                JobId = "job-456",
                ItemId = "item-789",
                ItemTitle = "Test Item",
                PurchasePrice = 25.99m,
                Quantity = 2,
                PurchaseDate = new DateTime(2024, 12, 19, 10, 30, 0),
                Status = "Completed",
                TransactionId = "txn-123",
                PaymentMethod = "PayPal",
                ShippingAddress = "123 Test St",
                Notes = "Test purchase"
            };
        }

        private Keyword2Find CreateTestKeyword()
        {
            return new Keyword2Find
            {
                Id = "keyword-123",
                Alias = "Test Product",
                Kws = "test,product,keywords",
                JobId = "job-456",
                RequiredQuantity = 5,
                PurchasedQuantity = 2,
                PriceMin = 10.0,
                PriceMax = 50.0,
                Categories4Api = "12345",
                EbaySiteName = "eBay US",
                LocatedIn = "United States",
                AvailableTo = "Worldwide",
                Zip = "12345",
                SellerType = "Include",
                ViewName = "TestView",
                SearchInDescription = true,
                Condition = new[] { "New", "Used" },
                Sellers = new[] { "seller1", "seller2" },
                ListingType = new[] { uBuyFirst.ListingType.AuctionsStartedNow, uBuyFirst.ListingType.BuyItNow }
            };
        }

        private KeywordSnapshot CreateTestKeywordSnapshot()
        {
            return new KeywordSnapshot
            {
                KeywordId = "keyword-123",
                Alias = "Test Product Snapshot",
                Keywords = "test,snapshot,keywords",
                RequiredQuantity = 8,
                PurchasedQuantity = 3,
                PriceMin = 15.0,
                PriceMax = 75.0,
                Categories = "67890",
                EbaySiteName = "eBay UK",
                LocatedIn = "United Kingdom",
                AvailableTo = "Europe",
                Zip = "SW1A 1AA",
                SellerType = "Exclude",
                ViewName = "SnapshotView",
                SearchInDescription = false,
                Condition = new[] { "New" },
                Sellers = new[] { "snapshot-seller" },
                ListingType = new[] { "BuyItNow" }
            };
        }
    }
}
